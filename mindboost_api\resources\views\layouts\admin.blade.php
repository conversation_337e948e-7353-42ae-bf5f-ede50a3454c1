<!doctype html>
<html lang="en" data-layout="vertical" data-sidebar="dark" data-sidebar-size="lg" data-preloader="disable" data-theme="default" data-topbar="light" data-bs-theme="light">

<head>

        <meta charset="utf-8">
        <title>{{ __(@$title) }}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- Favicon included -->
        <link rel="shortcut icon" href="{{getImageFile(get_option('app_fav_icon'))}}" type="image/x-icon">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- Apple touch icon included -->
        <link rel="apple-touch-icon" href="{{getImageFile(get_option('app_fav_icon'))}}">

        <meta content="Minimal Admin & Dashboard Template" name="description">
        <meta content="Themesbrand" name="author">
        <!-- App favicon -->
        <link rel="shortcut icon" href="{{asset('frontend/img/favicon.png') }}">
        @notifyCss
        <!-- Fonts css load -->
        <link rel="preconnect" href="{{asset('admin/fonts/fonts.googleapis.com/index.html') }}">
        <link id="fontsLink" href="{{asset('admin/fonts/fonts.googleapis.com/css2496c.css?family=Poppins:wght@300;400;500;600;700&amp;display=swap') }}" rel="stylesheet">

        <!-- Sweet Alert css-->
        <link href="{{asset('admin/libs/sweetalert2/sweetalert2.min.css') }}" rel="stylesheet" type="text/css" >

        <!-- jsvectormap css -->
        <link href="{{asset('admin/libs/jsvectormap/css/jsvectormap.min.css') }}" rel="stylesheet" type="text/css">

        <!--Swiper slider css-->
        <link href="{{asset('admin/libs/swiper/swiper-bundle.min.css') }}" rel="stylesheet" type="text/css">

        <!-- Layout config Js -->
        <script src="{{asset('admin/js/layout.js') }}"></script>
        <!-- Bootstrap Css -->
        <link href="{{asset('admin/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css">
        <!-- Icons Css -->
        <link href="{{asset('admin/css/icons.min.css') }}" rel="stylesheet" type="text/css">
        <!-- App Css-->
        <link href="{{asset('admin/css/app.min.css') }}" rel="stylesheet" type="text/css">
        <!-- custom Css-->
        <link href="{{asset('admin/css/custom.min.css') }}" rel="stylesheet" type="text/css">

        <link href="{{asset('admin/css/customs.css') }}" rel="stylesheet" type="text/css">

        @stack('style')

    </head>

    <body>
        <x-notify::notify />
        <!-- Begin page -->
        <div id="layout-wrapper">
            @include('admin.layouts.sidebar')
            @include('admin.layouts.navbar')

            <!-- ============================================================== -->
            <!-- Start right Content here -->
            <!-- ============================================================== -->
            <div class="main-content">

                @yield('content')

                @include('admin.layouts.footer')

            </div>
            <!-- end main content-->
        </div>
        <!-- END layout-wrapper -->

    <!--start back-to-top-->
    <button class="btn btn-dark btn-icon" id="back-to-top">
        <i class="bi bi-caret-up fs-3xl"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
        <div id="status">
            <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <div class="customizer-setting d-none d-md-block">
        <div class="btn btn-info p-2 text-uppercase rounded-end-0 shadow-lg" data-bs-toggle="offcanvas" data-bs-target="#theme-settings-offcanvas" aria-controls="theme-settings-offcanvas">
            <i class="bi bi-gear mb-1"></i> Customizer
        </div>
    </div>

    @notifyJs
    <script src="{{asset('admin/vendor/jquery/jquery-3.6.0.min.js')}}"></script>
    <!-- JAVASCRIPT -->
    <script src="{{asset('admin/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{asset('admin/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{asset('admin/js/plugins.js') }}"></script>

    <!-- apexcharts -->
    <script src="{{asset('admin/libs/apexcharts/apexcharts.min.js') }}"></script>

    <!-- Vector map-->
    <script src="{{asset('admin/libs/jsvectormap/js/jsvectormap.min.js') }}"></script>
    <script src="{{asset('admin/libs/jsvectormap/maps/world-merc.js') }}"></script>

    <!--Swiper slider js-->
    <script src="{{asset('admin/libs/swiper/swiper-bundle.min.js') }}"></script>

    <script src="{{asset('admin/libs/list.js/list.min.js') }}"></script>

    <!-- Dashboard init -->
    <script src="{{asset('admin/js/pages/dashboard-ecommerce.init.js') }}"></script>

    <script>
        var deleteTitle = '{{ __("Sure! You want to delete?") }}';
        var deleteText = '{{ __("You wont be able to revert this!") }}';
        var deleteConfirmButton = '{{ __("Yes, Delete It!") }}';
        var deleteSuccessText = '{{ __("Item has been deleted") }}';
    </script>

    <!-- Sweet Alerts js -->
    <script src="{{asset('admin/libs/sweetalert2/sweetalert2.min.js') }}"></script>

    <!-- Sweet alert init js-->
    <script src="{{asset('admin/js/pages/sweetalerts.init.js') }}"></script>

    <!-- App js -->
    <script src="{{asset('admin/js/app.js') }}"></script>

    <script src="{{asset('admin/js/admin-custom.js') }}">

    @stack('script')
</body>


</html>