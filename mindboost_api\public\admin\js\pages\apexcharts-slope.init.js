function getChartColorsArray(e){var t=document.getElementById(e);if(t){t=t.dataset.colors;if(t)return JSON.parse(t).map(e=>{var t=e.replace(/\s/g,"");return t.includes(",")?2===(e=e.split(",")).length?`rgba(${getComputedStyle(document.documentElement).getPropertyValue(e[0])}, ${e[1]})`:t:getComputedStyle(document.documentElement).getPropertyValue(t)||t});console.warn("data-colors attribute not found on: "+e)}}var basicSlopeChart="",chartMultiChart="";function loadCharts(){var e,t;(t=getChartColorsArray("basic_slope_chart"))&&(e={series:[{name:"Blue",data:[{x:"Jan",y:43},{x:"Feb",y:58}]},{name:"<PERSON>",data:[{x:"Jan",y:33},{x:"Feb",y:38}]},{name:"Red",data:[{x:"Jan",y:55},{x:"Feb",y:21}]}],chart:{height:350,width:400,type:"line"},plotOptions:{line:{isSlopeChart:!0}},colors:t},""!=basicSlopeChart&&basicSlopeChart.destroy(),(basicSlopeChart=new ApexCharts(document.querySelector("#basic_slope_chart"),e)).render());(t=getChartColorsArray("multi_group_chart"))&&(e={series:[{name:"Blue",data:[{x:"Category 1",y:503},{x:"Category 2",y:580},{x:"Category 3",y:135}]},{name:"Green",data:[{x:"Category 1",y:733},{x:"Category 2",y:385},{x:"Category 3",y:715}]},{name:"Orange",data:[{x:"Category 1",y:255},{x:"Category 2",y:211},{x:"Category 3",y:441}]},{name:"Yellow",data:[{x:"Category 1",y:428},{x:"Category 2",y:749},{x:"Category 3",y:559}]}],chart:{height:350,width:600,type:"line"},plotOptions:{line:{isSlopeChart:!0}},tooltip:{followCursor:!0,intersect:!1,shared:!0},dataLabels:{background:{enabled:!0},formatter(e,t){t=t.w.config.series[t.seriesIndex].name;return null!==e?t:""}},yaxis:{show:!0,labels:{show:!0}},xaxis:{position:"bottom"},legend:{show:!0,position:"top",horizontalAlign:"left"},stroke:{width:[2,3,4,2],dashArray:[0,0,5,2],curve:"smooth"},colors:t},""!=chartMultiChart&&chartMultiChart.destroy(),(chartMultiChart=new ApexCharts(document.querySelector("#multi_group_chart"),e)).render())}window.addEventListener("resize",function(){setTimeout(()=>{loadCharts()},250)}),loadCharts();