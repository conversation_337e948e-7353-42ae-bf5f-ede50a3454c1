@extends('layouts.admin')

@section('content')
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">{{ __('Update Password') }}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">{{__('Dashboard')}}</a></li>
                            <li class="breadcrumb-item active">{{ __('Update Password') }}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">{{ __('Update Password') }}</h4>
                    </div>
                    <div class="card-body">
                        <form action="{{route('admin.change-password')}}" method="post" class="form-horizontal" >
                            @csrf
                            <div class="row g-3">
                                <div class="col-lg-4">
                                    <div>
                                        <label for="exampleInputpassword" class="form-label">{{ __('Old Password') }} <span class="text-danger">*</span></label>
                                        <input type="password" name="old_password" id="old_password" value="" placeholder="{{ __('Type your old password') }}" class="form-control" required>
                                        @if ($errors->has('old_password'))
                                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('old_password') }}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div>
                                        <label for="exampleInputpassword" class="form-label">{{ __('New Password') }} <span class="text-danger">*</span></label>
                                        <input type="password" name="password" id="password" value="" placeholder="{{ __('Type your new password') }}" class="form-control" required>
                                        @if ($errors->has('password'))
                                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('password') }}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div>
                                        <label for="exampleInputpassword" class="form-label">{{ __('Confirm Password') }} <span class="text-danger">*</span></label>
                                        <input type="password" name="password_confirmation" id="password_confirmation" value="" placeholder="{{ __('Type your confirm password') }}" class="form-control" required>
                                        @if ($errors->has('password_confirmation'))
                                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('password_confirmation') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mt-3">
                                @updateButton
                            </div>
                        </form>
                    </div>
                    <!-- end card body -->
                </div>
                <!-- end card -->
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->


    </div>
    <!-- container-fluid -->
</div>
<!-- End Page-content -->
@endsection