<!-- ========== App Menu ========== -->
<div class="app-menu navbar-menu">
    <!-- LOGO -->
    <div class="navbar-brand-box">
        @if(get_option('app_logo') != '')   
            <a href="{{ route('admin.dashboard') }}" class="logo logo-dark">
                <span class="logo-sm">
                    <img class="img_hei_100" src="{{getImageFile(get_option('app_logo'))}}" alt="" height="100">
                </span>
                <span class="logo-lg">
                    <img class="img_hei_100" src="{{getImageFile(get_option('app_logo'))}}" alt="" height="100">
                </span>
            </a>
            <a href="{{ route('admin.dashboard') }}" class="logo logo-light">
                <span class="logo-sm">
                    <img class="img_hei_50" src="{{ asset('frontend/img/favicon.png') }}" alt="" height="50">
                </span>
                <span class="logo-lg">
                    <img class="img_hei_50" src="{{ asset('frontend/img/favicon.png') }}" alt="" height="50">
                </span>
            </a>
        @else
            <img src="" alt="">
        @endif
        <button type="button" class="btn btn-sm p-0 fs-3xl header-item float-end btn-vertical-sm-hover" id="vertical-hover">
            <i class="ri-record-circle-line"></i>
        </button>
    </div>

    <div id="scrollbar">
        <div class="container-fluid">

            <div id="two-column-menu">
            </div>
            <ul class="navbar-nav" id="navbar-nav">

                <li class="menu-title"><span data-key="t-menu">Menu</span></li>
                <li class="nav-item">
                    <a class="nav-link menu-link collapsed {{ active_if_full_match('admin/dashboard') }}" href="#sidebarDashboards">
                        <i class="ri-home-2-fill"></i> <span data-key="t-dashboards">{{ __('Dashboard') }}</span>
                    </a>
                </li>
                @can('user_management')
                    <li class="nav-item">
                        <a href="#sidebarInvoices" class="nav-link menu-link collapsed {{ @$navUserParentActiveClass }}" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarInvoices">
                            <i class="ph-user-circle-gear-thin"></i> <span data-key="t-invoices">{{__('Admin Management')}}</span>
                        </a>
                        <div class="collapse menu-dropdown" id="sidebarInvoices">
                            <ul class="nav nav-sm flex-column {{ @$navUserParentShowClass }}">
                                <li class="nav-item">
                                    <a href="{{route('user.create')}}" class="nav-link" data-key="t-list-view">{{__('Add User')}}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{route('user.index')}}" class="nav-link {{ @$subNavUserActiveClass }}" data-key="t-overview">{{__('All Users')}}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="apps-invoices-create.html" class="nav-link" data-key="t-create-invoice">Create Invoice</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                @endcan

                <li class="nav-item">
                    <a class="nav-link menu-link collapsed">
                        <span data-key="t-dashboards">{{ __('Software Version') }} {{ get_option('current_version', 1.0) }}</span>
                    </a>
                </li>

            </ul>
        </div>
        <!-- Sidebar -->
    </div>

    <div class="sidebar-background"></div>
</div>
<!-- Left Sidebar End -->