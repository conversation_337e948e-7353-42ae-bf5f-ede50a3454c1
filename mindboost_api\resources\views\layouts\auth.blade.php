<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="author" content="{{ get_option('app_copyright') }}">
    <meta name="robots" content="index, follow">
    <meta name="csrf-token" content="{{ csrf_token() }}" >
    <meta name="msapplication-TileImage" content="{{ getImageFile(get_option('app_logo')) }}">
    <meta name="msapplication-TileColor" content="rgba(103, 20, 222,.55)">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @php
        $metaData = getMeta('auth');
    @endphp
    <meta name="description" content="{{ $metaData['meta_description'] }}">
    <meta name="keywords" content="{{ $metaData['meta_keyword'] }}">
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:type" content="Learning">
    <meta property="og:title" content="{{ $metaData['meta_title'] }}">
    <meta property="og:description" content="{{ $metaData['meta_description'] }}">
    <meta property="og:image" content="{{ $metaData['og_image'] }}">
    <meta property="og:url" content="{{ url()->current() }}">

    <meta property="og:site_name" content="{{ get_option('app_name') }}">
    
    <!-- Twitter Card meta tags for Twitter sharing -->
    <meta name="twitter:card" content="Learning">
    <meta name="twitter:title" content="{{ $metaData['meta_title'] }}">
    <meta name="twitter:description" content="{{ $metaData['meta_description'] }}">
    <meta name="twitter:image" content="{{ $metaData['og_image'] }}">

    <title>{{ get_option('app_name') }} - {{ __(@$title) }}</title>

    @notifyCss

    <!-- Bootstrap CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/css/bootstrap.min.css') }}">

    <!-- Fontawesome CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/plugins/fontawesome/css/fontawesome.min.css') }}">
	<link rel="stylesheet" href="{{ asset('frontend/plugins/fontawesome/css/all.min.css') }}">

	<!-- Owl Carousel CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/css/owl.carousel.min.css') }}">
	<link rel="stylesheet" href="{{ asset('frontend/css/owl.theme.default.min.css') }}">

    <!-- Feathericon CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/css/feather.css') }}">

	<!-- Main CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/css/style.css') }}">

	<!-- Customs CSS -->
	<link rel="stylesheet" href="{{ asset('frontend/css/customs.css') }}">

    @stack('style')
</head>
<body>
    <x-notify::notify />
    @yield('content')
    
    @notifyJs
    <!-- jQuery -->
	<script src="{{ asset('frontend/js/jquery-3.7.1.min.js') }}" defer type="bece41af9cd1ce5b68d3a05e-text/javascript"></script>

    <!-- Bootstrap Core JS -->
	<script src="{{ asset('frontend/js/bootstrap.bundle.min.js') }}" defer type="bece41af9cd1ce5b68d3a05e-text/javascript"></script>

    <!-- Owl Carousel -->
	<script src="{{ asset('frontend/js/owl.carousel.min.js') }}" defer type="bece41af9cd1ce5b68d3a05e-text/javascript"></script>

    <!-- Validation-->
    <script src="{{ asset('frontend/js/validation.js') }}" type="e9049d94ae7191c6ad2b945a-text/javascript"></script>	

    <!-- Custom JS -->
	<script src="{{ asset('frontend/js/script.js') }}" defer type="bece41af9cd1ce5b68d3a05e-text/javascript"></script>

	<script src="{{ asset('frontend/js/rocket-loader.min.js') }}" defer data-cf-settings="bece41af9cd1ce5b68d3a05e-|49" defer></script>
    
    @stack('script')

</body>
</html>
