@extends('layouts.admin')

@section('content')
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">{{__('All Users')}}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{route('admin.dashboard')}}">{{__('Dashboard')}}</a></li>
                            <li class="breadcrumb-item active">{{__('All Users')}}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->

        <div id="instructorList">

            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center g-2">
                                <div class="col-lg-3 me-auto">
                                    <h6 class="card-title mb-0">{{__('All Users')}} <span class="badge bg-primary ms-1 align-baseline">{{$totalAdmin}}</span></h6>
                                </div><!--end col-->
                                <div class="col-xl-2 col-lg-3 col-sm-5">
                                    <div class="search-box">
                                        <input type="text" class="form-control search" placeholder="Rechercher...">
                                        <i class="ri-search-line search-icon"></i>
                                    </div>
                                </div><!--end col-->
                                <div class="col-sm-auto">
                                    <div class="hstack gap-2">
                                        <button class="btn btn-subtle-danger" id="remove-actions"><i class="ri-delete-bin-2-line"></i></button>
                                        <a href="{{route('user.create')}}" class="btn btn-secondary"><i class="bi bi-plus-circle align-baseline me-1"></i> {{__('Add User')}}</a>
                                    </div>
                                </div><!--end col-->
                            </div>
                        </div>
                        <div class="card-body mt-3">
                            <div class="table-responsive table-card">
                                <table class="table table-centered align-middle table-custom-effect table-nowrap mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="option" id="checkAll">
                                                    <label class="form-check-label" for="checkAll"></label>
                                                </div>
                                            </th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="instructor_id">ID</th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="instructor">{{__('Name')}}</th> 
                                            <th scope="col" class="sort cursor-pointer" data-sort="email">{{__('Email')}}</th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="experience">{{__('Address')}}</th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="students">{{__('Phone')}}</th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="contact">{{__('Role')}}</th>
                                            <th scope="col" class="sort cursor-pointer" data-sort="rating">{{__('Status')}}</th>
                                            <th scope="col">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody class="list form-check-all">
                                        @foreach($users as $user)
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="record_ids[]" value="{{ $user->id }}">
                                                        <label class="form-check-label"></label>
                                                    </div>
                                                </td>
                                                <td class="instructor_id"><a href="#" class="fw-medium link-primary">{{$user->id}}</a></td>
                                                <td class="instructor">
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <div class="flex-shrink-0">
                                                            <img src="{{getImageFile(get_option('app_logo'))}}" alt="" class="avatar-xxs rounded-circle">
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <a href="#" class="text-reset">{{$user->first_name.' '.$user->last_name}}</a>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="email">{{$user->email}}</td>
                                                <td class="experience">{{$user->address}}</td>
                                                <td class="students">{{$user->phone_number}}</td>
                                                <td class="contact">@if(count($user->getRoleNames()) > 0) <i class="bi bi-star-fill text-warning align-baseline me-1"></i> {{$user->getRoleNames()[0] }}@endif</td>
                                                <td class="status">
                                                    @if($user->deleted_at == null)
                                                        <span class="badge bg-success-subtle text-success">Actif</span>
                                                    @else
                                                        <span class="badge bg-danger-subtle text-danger">Supprimé</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($user->deleted_at == null)
                                                        <ul class="d-flex gap-2 list-unstyled mb-0">
                                                            <li>
                                                                <a href="{{route('user.edit', [$user->id])}}" class="btn btn-subtle-primary btn-icon btn-sm edit-item-btn"><i class="ph-pencil"></i></a>
                                                            </li>
                                                            <li>
                                                                <a href="javascript:void(0);" data-url="{{route('user.delete', [$user->id])}}" class="btn btn-subtle-danger btn-icon btn-sm remove-item-btn delete"><i class="ph-trash"></i></a>
                                                            </li>
                                                        </ul>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody><!-- end tbody -->
                                </table><!-- end table -->
                                <div class="noresult" style="display: none">
                                    <div class="text-center py-4">
                                        <i class="ph-magnifying-glass fs-1 text-primary"></i>
                                        <h5 class="mt-2">Désolé! Aucun résultat trouvé</h5>
                                        <p class="text-muted mb-0">Nous avons recherché plus de 100 administrateurs. Nous n'avons trouvé aucun administrateur pour votre recherche.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row align-items-center mt-4 pt-3" id="pagination-element">
                                <div class="col-sm-auto mt-3 mt-sm-0">
                                    <div class="pagination-wrap hstack justify-content-center gap-2">
                                        {{$users->links()}}
                                    </div>
                                </div><!--end col-->
                            </div><!--end row-->
                        </div>
                    </div>
                </div><!--end col-->
            </div><!--end row-->

        </div>

    </div>
    <!-- container-fluid -->
</div>
<!-- End Page-content -->
@endsection
@push('script')
    <script>
        /*$(document).ready(function () {
            // Sélectionner/Désélectionner toutes les cases
            $('#checkAll').on('change', function () {alert('ccc')
                $('.form-check-input').prop('checked', $(this).prop('checked'));
            });

            // Supprimer les enregistrements sélectionnés
            $('#deleteSelected').on('click', function () {
                let selectedIds = [];
                $('.recordCheckbox:checked').each(function () {
                    selectedIds.push($(this).val());
                });

                if (selectedIds.length === 0) {
                    alert('Veuillez sélectionner au moins un enregistrement.');
                    return;
                }

                if (!confirm('Voulez-vous vraiment supprimer les enregistrements sélectionnés ?')) {
                    return;
                }

                $.ajax({
                    url: '',
                    type: 'DELETE',
                    data: {
                        record_ids: selectedIds,
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        alert(response.message);
                        location.reload(); // Recharger la page
                    },
                    error: function (xhr) {
                        alert(xhr.responseJSON.message);
                    }
                });
            });

            // Supprimer un seul enregistrement
            $('.deleteSingle').on('click', function () {
                let recordId = $(this).data('id');

                if (!confirm('Voulez-vous vraiment supprimer cet enregistrement ?')) {
                    return;
                }

                $.ajax({
                    url: `/records/${recordId}`,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        alert(response.message);
                        location.reload(); // Recharger la page
                    },
                    error: function (xhr) {
                        alert(xhr.responseJSON.message);
                    }
                });
            });
        });*/

    </script>
@endpush