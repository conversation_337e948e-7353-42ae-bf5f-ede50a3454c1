@extends('layouts.admin')

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">{{ __('Dashboard') }}</h4>

                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">{{ get_option('app_name') }}</a></li>
                                <li class="breadcrumb-item active">{{ __('Dashboard') }}</li>
                            </ol>
                        </div>

                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-xl-4">
                    <div class="card card-height-100 border-0 overflow-hidden">
                        <div class="card-body p-0">
                            <div class="row g-0">
                                <div class="col-md-6">
                                    <!-- card -->
                                    <div class="card shadow-none border-end-md border-bottom rounded-0 mb-0">
                                        <div class="card-body">
                                            <div class="avatar-sm">
                                                <span class="avatar-title bg-primary-subtle text-primary rounded-circle fs-3">
                                                    <i class="ph-user-circle-gear-thin"></i>
                                                </span>
                                            </div>
                                            <div class="mt-4">
                                                <p class="text-uppercase fw-medium text-muted text-truncate fs-sm">{{ __('Total Admin') }}</p>
                                                <h4 class="fw-semibold mb-3"><span class="counter-value" data-target="{{ $total_admins }}">{{ $total_admins }}</span></h4>
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->
                                </div><!-- end col -->
                                <div class="col-md-6">
                                    <!-- card -->
                                    <div class="card shadow-none border-bottom rounded-0 mb-0">
                                        <div class="card-body">
                                            <div class="avatar-sm">
                                                <span class="avatar-title bg-dark-subtle text-dark rounded-circle fs-3">
                                                    <i class="ph-user-circle"></i>
                                                </span>
                                            </div>
                                            <div class="mt-4">
                                                <p class="text-uppercase fw-medium text-muted text-truncate fs-sm">{{ __('Total Instructors') }}</p>
                                                <h4 class="fw-semibold mb-3"><span class="counter-value" data-target="{{ $total_instructors }}">{{ $total_instructors }}</span></h4>
                                                
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->
                                </div><!-- end col -->
                                <div class="col-md-6">
                                    <!-- card -->
                                    <div class="card shadow-none border-end-md rounded-0 mb-0">
                                        <div class="card-body">
                                            <div class="avatar-sm">
                                                <span class="avatar-title bg-light text-body rounded-circle fs-3">
                                                    <i class="ph-user-square"></i>
                                                </span>
                                            </div>
                                            <div class="mt-4">
                                                <p class="text-uppercase fw-medium text-muted text-truncate fs-sm">{{ __('Total Students') }}</p>
                                                <h4 class="fw-semibold mb-3"><span class="counter-value" data-target="{{ $total_students }}">0</span></h4>
                                                
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->
                                </div><!-- end col -->

                                <div class="col-md-6">
                                    <!-- card -->
                                    <div class="card shadow-none border-top border-top-md-0 rounded-0 mb-0">
                                        <div class="card-body">
                                            <div class="avatar-sm">
                                                <span class="avatar-title bg-info-subtle text-info rounded-circle fs-3">
                                                    <i class="ph-books-thin"></i>
                                                </span>
                                            </div>
                                            <div class="mt-4">
                                                <p class="text-uppercase fw-medium text-muted text-truncate fs-sm">{{ __('Total Courses') }}</p>
                                                <h4 class="fw-semibold mb-3"><span class="counter-value" data-target="{{ $total_courses }}">0</span> </h4>
                                                
                                            </div>
                                        </div><!-- end card body -->
                                    </div><!-- end card -->
                                </div><!-- end col -->
                            </div> <!-- end row-->
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-xl-8">
                    <div class="card">
                        <div class="row g-0">
                            <div class="col-xl-9">
                                <div class="card-header border-0 align-items-center d-flex">
                                    <h4 class="card-title mb-0 flex-grow-1">Revenue</h4>
                                    <div>
                                        <button type="button" class="btn btn-subtle-secondary btn-sm">
                                            ALL
                                        </button>
                                        <button type="button" class="btn btn-subtle-secondary btn-sm">
                                            1M
                                        </button>
                                        <button type="button" class="btn btn-subtle-secondary btn-sm">
                                            6M
                                        </button>
                                        <button type="button" class="btn btn-subtle-primary btn-sm">
                                            1Y
                                        </button>
                                    </div>
                                </div><!-- end card header -->
                                <div class="card-body ps-0">
                                    <div class="w-100">
                                        <div id="market-overview" data-colors='["--tb-primary", "--tb-secondary"]' class="apex-charts" dir="ltr"></div>
                                    </div>
                                </div><!-- end card body -->
                            </div>
                            <div class="col-xl-3">
                                <div class="card-body border-start-xl border-top border-top-xl-0 border-2 h-100">
                                    <div>
                                        <p class="text-muted mb-2">Budget (USD)</p>
                                        <h4>$750.36M <small class="text-success fs-sm fw-normal"><i class="ph-arrow-up align-baseline"></i> 2.17%</small></h4>
                                        <p class="text-muted text-truncate">Budget in than last years</p>
                                        <div class="mx-3">
                                            <div id="mini-chart-6" data-colors='["--tb-primary"]' class="apex-charts" dir="ltr"></div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <p class="text-muted mb-2">Payouts (USD)</p>
                                        <h4>$7,45,123 <small class="text-danger fs-sm fw-normal"><i class="ph-arrow-down align-baseline"></i> -1.36%</small></h4>
                                        <p class="text-muted text-truncate">Payouts in than last years</p>
                                        <div class="mx-3">
                                            <div id="mini-chart-7" data-colors='["--tb-info"]' class="apex-charts" dir="ltr"></div>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#addAmount">Add Amount</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- end card -->
                </div><!-- end col -->
            </div><!--end row-->

        </div>
        <!-- container-fluid -->
    </div>
    <!-- End Page-content -->
@endsection