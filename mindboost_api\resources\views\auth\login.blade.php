@extends('layouts.auth')

@section('content')
<!-- Main Wrapper -->
    <div class="main-wrapper log-wrap">
    
        <div class="row">
        
            <!-- Login Banner -->
            <div class="col-md-6 login-bg">
                <div class="owl-carousel login-slide owl-theme">
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Login Banner -->
            
            <div class="col-md-6 login-wrap-bg">		
            
                <!-- Login -->
                <div class="login-wrapper">
                    <div class="loginbox">
                        <div class="img-logo">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                            <div class="back-home">
                                <a href="{{ route('main.index') }}">Page d'accueil</a>
                            </div>
                        </div>
                        <h1>{{__('Connectez-vous à votre compte')}}</h1>
                        <form method="POST" action="{{ route('login') }}">
                        @csrf
                            <div class="input-block">
                                <label class="form-control-label">{{__('Email ou Téléphone')}}</label>
                                <input type="email" name="email" value="{{old('email')}}" class="form-control" placeholder="{{ __('Tapez votre mail ou votre numéro de téléphone') }}">
                                @if ($errors->has('email'))
                                    <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('email') }}</span>
                                @endif
                            </div>
                            <div class="input-block">
                                <label class="form-control-label">{{__('Mot de passe')}}</label>
                                <div class="pass-group">
                                    <input type="password" class="form-control pass-input"
                                        name="password" value="{{old('password')}}" placeholder="*********">
                                    <span class="feather-eye toggle-password"></span>
                                </div>
                                @if ($errors->has('password'))
                                    <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('password') }}</span>
                                @endif
                            </div>
                            <div class="forgot">
                                <span><a class="forgot-link" href="{{ route('forget-password') }}">{{__('Mot de passe oublié')}}?
                                        ?</a></span>
                            </div>
                            {{--<div class="remember-me">
                                <label class="custom_check mr-2 mb-0 d-inline-flex remember-me"> Remember me
                                    <input type="checkbox" name="radio">
                                    <span class="checkmark"></span>
                                </label>
                            </div>--}}
                            <div class="d-grid">
                                <button class="btn btn-primary btn-start" type="submit">Se connecter</button>
                            </div>
                        </form>
                    </div>
                    <div class="google-bg text-center">
                        {{--<span><a href="#">Or sign in with</a></span>
                        <div class="sign-google">
                            <ul>
                                <li><a href="#"><img src="assets/img/net-icon-01.png" class="img-fluid" alt="Logo"> Sign In using Google</a></li>
                                <li><a href="#"><img src="assets/img/net-icon-02.png" class="img-fluid" alt="Logo">Sign In using Facebook</a></li>
                            </ul>
                        </div>--}}
                        <p class="mb-0">{{__('Nouvel utilisateur')}} ?  <a href="{{route('sign-up')}}">{{__('Créer un compte')}}</a></p>
                    </div>
                </div>
                <!-- /Login -->
                
            </div>
            
        </div>
        
    </div>
<!-- /Main Wrapper -->
@endsection