@extends('layouts.admin')

@section('content')
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">{{__('Add User')}}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                            <li class="breadcrumb-item active">{{__('Add User')}}</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title mb-0">{{__('Add User')}}</h4>
                    </div><!-- end card header -->

                    <div class="card-body">
                        
                        <form action="{{route('user.store')}}" method="post" enctype="multipart/form-data" class="row g-3 needs-validation" novalidate>
                            @csrf
                            <div class="col-md-4">
                                <label for="firstname" class="form-label">{{__('First Name')}}</label>
                                <input type="text" class="form-control" name="firstname" placeholder=" {{__('First Name')}} " id="firstname" value="{{old('firstname')}}" required>
                                <div class="valid-feedback">
                                    Parfait
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez entrer le prénom.
                                </div>
                                @if ($errors->has('firstname'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('firstname') }}</span>
                                @endif
                            </div>
                            <div class="col-md-4">
                                <label for="lastname" class="form-label">{{__('Last Name')}}</label>
                                <input type="text" class="form-control" name="lastname" placeholder=" {{__('Last Name')}} " id="lastname" value="{{old('lastname')}}" required>
                                <div class="valid-feedback">
                                    Parfait
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez entrer le nom.
                                </div>
                                @if ($errors->has('lastname'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('lastname') }}</span>
                                @endif
                            </div>
                            <div class="col-md-4">
                                <div>
                                    <label for="iconInput" class="form-label">{{__('Email')}}</label>
                                    <div class="form-icon">
                                        <input type="email" class="form-control form-control-icon" name="email" placeholder=" {{__('Email')}} " id="email" value="{{old('email')}}">
                                        <i class="ri-mail-unread-line"></i>
                                    </div>
                                    <div class="valid-feedback">
                                        Parfait
                                    </div>
                                    <div class="invalid-feedback">
                                        Veuillez entrer le mail.
                                    </div>
                                    @if ($errors->has('email'))
                                    <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                        $errors->first('email') }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="area_code" class="form-label">{{ __('Area Code')}}</label>
                                <div class="input-group">
                                    <label class="input-group-text" for="area_code">Options</label>
                                    <select class="form-select" name="area_code" id="area_code">
                                        <option value>{{ __("Select Code") }}</option>
                                        @foreach ($countries as $country)
                                        <option value="{{ $country->phonecode }}" @if(old('area_code')==$country->
                                            phonecode) selected @endif>{{ $country->short_name.'('.$country->phonecode.')' }}</option>
                                        @endforeach
                                    </select>
                                    <div class="valid-feedback">
                                        Parfait
                                    </div>
                                    <div class="invalid-feedback">
                                        Veuillez choisir un indicatif.
                                    </div>
                                </div>
                                @if ($errors->has('area_code'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('area_code') }}</span>
                                @endif
                            </div>
                            <div class="col-md-4">
                                <label for="phone_number" class="form-label">{{__('Phone')}}</label>
                                <div class="input-group has-validation">
                                    <span class="input-group-text" id="phone_number">Tel</span>
                                    <input type="text" class="form-control" placeholder=" {{__('Phone')}} " name="phone_number" id="phone_number" value="{{old('phone_number')}}" aria-describedby="inputGroupPrepend" required>
                                    <div class="valid-feedback">
                                        Parfait
                                    </div>
                                    <div class="invalid-feedback">
                                        Veuillez entrer un numéro de téléphone.
                                    </div>
                                    @if ($errors->has('phone_number'))
                                    <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                        $errors->first('phone_number') }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="address" class="form-label">{{__('Address')}}</label>
                                <textarea name="address" id="address" required class="form-control"
                                    placeholder="Adresse">{{old('address')}}</textarea>
                                @if ($errors->has('address'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('address') }}</span>
                                @endif
                                <div class="valid-feedback">
                                    Parfait
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez entrer une adresse.
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="role_name" class="form-label">{{__('Select Role')}}</label>
                                <select class="form-select" name="role_name" id="role_name" required>
                                    <option value="">{{__('Select Role')}}</option>
                                    @foreach($roles as $role)
                                    <option value="{{$role->name}}" {{old('role_name')==$role->name ? 'selected' : '' }}
                                        >{{$role->name}}</option>
                                    @endforeach
                                </select>
                                @if ($errors->has('role_name'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('role_name') }}</span>
                                @endif
                                <div class="valid-feedback">
                                    Parfait
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez choisir un rôle.
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="password" class="form-label">{{__('Password')}}</label>
                                <input type="text" class="form-control" name="password" id="password" placeholder=" {{__('Password')}} " required>
                                <div class="valid-feedback">
                                    Parfait
                                </div>
                                <div class="invalid-feedback">
                                    Veuillez entrer un mot de passe.
                                </div>
                                @if ($errors->has('password'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('password') }}</span>
                                @endif
                            </div>
                            
                            <div class="col-12">
                                @saveWithAnotherButton
                            </div>
                        </form>
                    </div>
                </div>
            </div> <!-- end col -->

        </div>

    </div> <!-- container-fluid -->
</div>
<!-- End Page-content -->
@endsection
@push('script')
    <!-- prismjs plugin -->
    <script src="{{asset('admin/libs/prismjs/prism.js') }}"></script>

    <script src="{{asset('admin/js/pages/form-validation.init.js') }}"></script>
    
@endpush