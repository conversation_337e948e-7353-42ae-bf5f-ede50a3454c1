<?php

use App\Http\Controllers\Frontend\RegistrationController;
use App\Http\Controllers\Frontend\MainIndexController;
use App\Http\Controllers\Auth\LoginController;

Route::group(['middleware' => 'guest'], function () {
    Route::get('sign-up', [RegistrationController::class, 'signUp'])->name('sign-up');
    Route::post('store-sign-up', [RegistrationController::class, 'storeSignUp'])->name('store.sign-up');
});

Route::get('forget-password', [LoginController::class, 'forgetPassword'])->name('forget-password');
Route::get('user/email/verify/{token}', [RegistrationController::class, 'emailVerification'])->name('user.email.verification');

Route::group(['middleware' => 'private.mode'], function () {
    Route::get('/', [MainIndexController::class, 'index'])->name('main.index');

    Route::get('terms-conditions', [MainIndexController::class, 'termConditions'])->name('terms-conditions')->withoutMiddleware('private.mode');
    Route::get('privacy-policy', [MainIndexController::class, 'privacyPolicy'])->name('privacy-policy')->withoutMiddleware('private.mode');
});