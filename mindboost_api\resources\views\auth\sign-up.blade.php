@extends('layouts.auth')

@section('content')
<!-- Main Wrapper -->
    <div class="main-wrapper log-wrap">
    
        <div class="row">
        
            <!-- Login Banner -->
            <div class="col-md-6 login-bg">
                <div class="owl-carousel login-slide owl-theme">
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                    <div class="welcome-login">
                        <div class="login-banner">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                        </div>
                        <div class="mentor-course text-center">
                            <h2>Bienvenue sur <br>MindBoost.</h2>
                            <p>Préparez vos concours avec des ressources, exercices et conseils pour réussir. Rejoignez-nous et boostez vos chances de succès !</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Login Banner -->
            
            <div class="col-md-6 login-wrap-bg">		
            
                <!-- Login -->
                <div class="login-wrapper">
                    <div class="loginbox">
                        <div class="img-logo">
                            <img src="{{getImageFile(get_option('app_black_logo'))}}" class="img-fluid" alt="Logo">
                            <div class="back-home">
                                <a href="{{ route('main.index') }}">Page d'accueil</a>
                            </div>
                        </div>
                        <h1>{{__('Inscrivez-vous')}}</h1>
                        <form method="POST" action="{{route('store.sign-up')}}">
                        @csrf
                            
                            <div class="input-block">
                                <label class="form-control-label">{{__('Email')}} <span class="text-danger">*</span></label>
                                <input type="email" required name="email" id="email" value="{{old('email')}}" class="form-control" placeholder="Entrer votre adresse mail">
                                @if ($errors->has('email'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('email') }}</span>
                                @endif
                            </div>
                            <div class="input-block">
                                <label class="form-control-label">{{__('Prénom')}} <span class="text-danger">*</span></label>
                                <input type="text" name="first_name" required id="first_name" value="{{old('first_name')}}" class="form-control" placeholder="{{__('Prénom')}}">
                                @if ($errors->has('first_name'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('first_name') }}</span>
                                @endif
                            </div>
                            <div class="input-block">
                                <label class="form-control-label">{{__('Nom')}} <span class="text-danger">*</span></label>
                                <input type="text" name="last_name" required id="last_name" value="{{old('last_name')}}" class="form-control" placeholder="{{__('Nom')}}">
                                @if ($errors->has('last_name'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('last_name') }}</span>
                                @endif
                            </div>
                            <div class="input-block">
                                <label class="form-control-label">{{__('Mot de passe')}} <span class="text-danger">*</span></label>
                                <div class="pass-group" id="passwordInput">																	
                                    <input type="password" required name="password" id="password" value="{{old('password')}}" class="form-control pass-input" placeholder="*********">
                                    <span class="toggle-password feather-eye"></span>
                                    <span class="pass-checked"><i class="feather-check"></i></span>
                                </div>
                                @if ($errors->has('password'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                    $errors->first('password') }}</span>
                                @endif
                                <div  class="password-strength" id="passwordStrength">
                                    <span id="poor"></span>
                                    <span id="weak"></span>
                                    <span id="strong"></span>
                                    <span id="heavy"></span>
                                </div>
                                <div id="passwordInfo"></div>	
                            </div>

                            <div class="form-check remember-me">
                                <label class="form-check-label mb-0" for="flexCheckChecked">
                                    <input class="form-check-input" required value="" id="flexCheckChecked" checked type="checkbox" name="remember"> En cliquant sur Créer un compte, je reconnais avoir lu et accepté les 
                                     <a
                                    href="{{ route('terms-conditions') }}"
                                    class="color-hover text-decoration-underline">les conditions d'utilisation</a> et  <a
                                    href="{{ route('privacy-policy') }}"
                                    class="color-hover text-decoration-underline">la politique de confidentialité.</a>
                                </label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-start" type="submit">{{__('S\'inscrire')}}</button>
                            </div>
                        </form>
                    </div>
                    <div class="google-bg text-center">
                        {{--<span><a href="#">Or sign in with</a></span>
                        <div class="sign-google">
                            <ul>
                                <li><a href="#"><img src="assets/img/net-icon-01.png" class="img-fluid" alt="Logo"> Sign In using Google</a></li>
                                <li><a href="#"><img src="assets/img/net-icon-02.png" class="img-fluid" alt="Logo">Sign In using Facebook</a></li>
                            </ul>
                        </div>--}}
                        <p class="mb-0">{{__('Vous avez déjà un compte?')}} <a href="{{route('login')}}">{{__('Se connecter')}}</a></p>
                    </div>
                </div>
                <!-- /Login -->
                
            </div>
            
        </div>
        
    </div>
<!-- /Main Wrapper -->
@endsection